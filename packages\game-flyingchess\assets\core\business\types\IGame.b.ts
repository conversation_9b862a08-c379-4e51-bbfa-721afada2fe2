import { Vec2, v2 } from 'cc'

type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never }

export type XOR<T, U> = (Without<T, U> & U) | (Without<U, T> & T)

/**手牌显示状态 */
export enum HandCardStatus {
    SHOW,
    HIDE,
}

export type Single = 2 | 3 | 4

const head_y_pos = 0

/**玩家初始位置， 在一行上 */
export const GameModelSinglePosMap: Record<Single, Vec2[]> = {
    2: [v2(-100, head_y_pos), v2(100, head_y_pos)],
    3: [v2(-130, head_y_pos), v2(0, head_y_pos), v2(130, head_y_pos)],
    4: [
        v2(-195, head_y_pos),
        v2(-65, head_y_pos),
        v2(65, head_y_pos),
        v2(195, head_y_pos),
    ],
}

//头像默认位置，分布在正方形的四个对角
export const HeadNodeDefaultPosMap: Record<4, Vec2[]> = {
    4: [
        v2(-310, 350), // 左上角
        v2(310, 350), // 右上角
        v2(310, -270), // 右下角
        v2(-310, -270), // 左下角
    ],
}

//卡牌位置， 假设卡牌200*300
const card_top_y_pos = 830
const card_bot_y_pos = 500
export const CardNodeSinglePosMap: Record<4, Vec2[]> = {
    //横向间隔40，纵向间隔30
    4: [
        v2(-120, card_top_y_pos),
        v2(120, card_top_y_pos),
        v2(-120, card_bot_y_pos),
        v2(120, card_bot_y_pos),
    ],
}

//卡牌缩略图时的位置
export const CardNodeThumbSinglePosMap: Record<4, Vec2[]> = {
    //横向间隔145
    4: [v2(-218, 0), v2(-73, 0), v2(73, 0), v2(218, 0)],
}

/**单人模式 */
export const GameSingle: Record<Single, string> = {
    2: '1V1',
    3: '1V1V1',
    4: '1V1V1V1',
}

export type ServerType = 'Game' | 'Tourist' | null

/**游戏关闭类型 */
export enum GameCloseType {
    /**加入游戏超时 */
    JoinOverTime = 'join_over_time',
    /**结束 */
    GameOver = 'game_over',
}
